<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>声音复刻试听下载</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        
        .container {
            background-color: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        h1 {
            text-align: center;
            color: #333;
            margin-bottom: 30px;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #555;
        }
        
        input[type="text"], textarea {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 14px;
            box-sizing: border-box;
        }
        
        textarea {
            resize: vertical;
            min-height: 80px;
        }
        
        .char-count {
            text-align: right;
            font-size: 12px;
            color: #666;
            margin-top: 5px;
        }
        
        .generate-btn {
            width: 100%;
            padding: 12px;
            background-color: #007bff;
            color: white;
            border: none;
            border-radius: 5px;
            font-size: 16px;
            cursor: pointer;
            transition: background-color 0.3s;
        }
        
        .generate-btn:hover {
            background-color: #0056b3;
        }
        
        .generate-btn:disabled {
            background-color: #ccc;
            cursor: not-allowed;
        }
        
        .loading {
            display: none;
            text-align: center;
            margin: 20px 0;
        }
        
        .spinner {
            border: 4px solid #f3f3f3;
            border-top: 4px solid #3498db;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 2s linear infinite;
            margin: 0 auto;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .audio-section {
            display: none;
            margin-top: 30px;
            padding: 20px;
            background-color: #f8f9fa;
            border-radius: 5px;
        }
        
        .audio-controls {
            display: flex;
            align-items: center;
            gap: 15px;
            flex-wrap: wrap;
        }
        
        audio {
            flex: 1;
            min-width: 300px;
        }
        
        .download-btn {
            padding: 8px 16px;
            background-color: #28a745;
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
            transition: background-color 0.3s;
        }
        
        .download-btn:hover {
            background-color: #218838;
        }
        
        .error-message {
            display: none;
            color: #dc3545;
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            padding: 10px;
            border-radius: 5px;
            margin-top: 15px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>声音复刻试听下载</h1>
        
        <form id="voiceForm">
            <div class="form-group">
                <label for="voiceId">声音ID *</label>
                <input type="text" id="voiceId" name="voiceId" required placeholder="请输入声音ID">
            </div>
            
            <div class="form-group">
                <label for="textContent">生成文本 *</label>
                <textarea id="textContent" name="textContent" required placeholder="请输入要生成语音的文本内容" maxlength="100"></textarea>
                <div class="char-count">
                    <span id="charCount">0</span>/100
                </div>
            </div>
            
            <button type="submit" class="generate-btn" id="generateBtn">生成</button>
        </form>
        
        <div class="loading" id="loading">
            <div class="spinner"></div>
            <p>正在生成语音，请稍候...</p>
        </div>
        
        <div class="error-message" id="errorMessage"></div>
        
        <div class="audio-section" id="audioSection">
            <h3>试听音频</h3>
            <div class="audio-controls">
                <audio id="audioPlayer" controls>
                    您的浏览器不支持音频播放。
                </audio>
                <button class="download-btn" id="downloadBtn">下载</button>
            </div>
        </div>
    </div>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/pako/2.1.0/pako.min.js"></script>
    <script>
        // WebSocket 相关常量和配置
        const appid = '8359578722';
        const token = 'Zol8wcj36NJGQC141tLZgNkvdVg-o_a7';
        const cluster = 'volcano_icl';
        const host = 'openspeech.bytedance.com';
        const api_url = `wss://${host}/api/v1/tts/ws_binary`;
        
        // 默认请求头
        const default_header = new Uint8Array([0x11, 0x10, 0x11, 0x00]);
        
        // 请求模板
        const request_json = {
            app: {
                appid: appid,
                token: 'access_token',
                cluster: cluster,
            },
            user: {
                uid: '388808087185088',
            },
            audio: {
                voice_type: 'xxx',
                encoding: 'mp3',
                speed_ratio: 1.0,
                volume_ratio: 1.0,
                pitch_ratio: 1.0,
            },
            request: {
                reqid: 'xxx',
                text: '',
                text_type: 'ssml',
                operation: 'xxx',
            },
        };
        
        // 生成UUID
        function generateUUID() {
            return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
                var r = Math.random() * 16 | 0, v = c == 'x' ? r : (r & 0x3 | 0x8);
                return v.toString(16);
            });
        }
        
        // 解析WebSocket响应
        function parseResponse(buffer, audioChunks) {
            const view = new DataView(buffer);
            const protocol_version = (view.getUint8(0) >> 4) & 0x0f;
            const header_size = view.getUint8(0) & 0x0f;
            const message_type = (view.getUint8(1) >> 4) & 0x0f;
            const message_type_specific_flags = view.getUint8(1) & 0x0f;
            const serialization_method = (view.getUint8(2) >> 4) & 0x0f;
            const message_compression = view.getUint8(2) & 0x0f;
            const reserved = view.getUint8(3);
            
            const payload_start = header_size * 4;
            const payload = buffer.slice(payload_start);
            
            if (message_type === 0xb) {
                // audio-only server response
                if (message_type_specific_flags === 0) {
                    // no sequence number as ACK
                    return false;
                } else {
                    const payload_view = new DataView(payload);
                    const sequence_number = payload_view.getInt32(0, false); // big endian
                    const payload_size = payload_view.getUint32(4, false); // big endian
                    const audio_payload = payload.slice(8);
                    
                    // 将音频数据添加到数组中
                    audioChunks.push(audio_payload);
                    
                    return sequence_number < 0; // 如果序列号小于0，表示结束
                }
            } else if (message_type === 0xf) {
                // 错误消息
                const payload_view = new DataView(payload);
                const code = payload_view.getUint32(0, false);
                const msg_size = payload_view.getUint32(4, false);
                let error_msg = payload.slice(8);
                
                // 解压缩错误消息
                if (message_compression === 1) {
                    error_msg = pako.ungzip(error_msg);
                }
                const decoder = new TextDecoder('utf-8');
                error_msg = decoder.decode(error_msg);
                
                throw new Error(`服务器错误 ${code}: ${error_msg}`);
            } else if (message_type === 0xc) {
                // frontend server response
                const payload_view = new DataView(payload);
                const msg_size = payload_view.getUint32(0, false);
                let frontend_payload = payload.slice(4);
                
                // 解压缩消息
                if (message_compression === 1) {
                    frontend_payload = pako.ungzip(frontend_payload);
                }
                const decoder = new TextDecoder('utf-8');
                const message = decoder.decode(frontend_payload);
                console.log('Frontend message:', message);
                return false;
            }
            
            return false;
        }
        
        // 生成语音
        async function generateVoice(voiceType, text) {
            return new Promise((resolve, reject) => {
                const query_request_json = JSON.parse(JSON.stringify(request_json));
                query_request_json.audio.voice_type = voiceType;
                query_request_json.request.reqid = generateUUID();
                query_request_json.request.operation = 'query';
                query_request_json.request.text = text;
                
                // 将JSON转换为字节并压缩
                const json_str = JSON.stringify(query_request_json);
                const encoder = new TextEncoder();
                let payload_bytes = encoder.encode(json_str);
                payload_bytes = pako.gzip(payload_bytes);
                
                // 构建完整请求
                const payload_length = payload_bytes.length;
                const full_request = new Uint8Array(8 + payload_length);
                
                // 设置头部
                full_request.set(default_header, 0);
                
                // 设置payload长度 (big endian)
                const length_view = new DataView(full_request.buffer, 4, 4);
                length_view.setUint32(0, payload_length, false);
                
                // 设置payload
                full_request.set(payload_bytes, 8);
                
                // 创建WebSocket连接 (注意：浏览器WebSocket不支持自定义头部，需要通过URL参数传递token)
                const ws_url = `${api_url}?authorization=Bearer%3B%20${encodeURIComponent(token)}`;
                const ws = new WebSocket(ws_url);
                
                const audioChunks = [];
                
                ws.onopen = () => {
                    console.log('WebSocket连接已建立');
                    ws.send(full_request);
                };
                
                ws.onmessage = (event) => {
                    try {
                        const done = parseResponse(event.data, audioChunks);
                        if (done) {
                            // 合并所有音频块
                            const totalLength = audioChunks.reduce((sum, chunk) => sum + chunk.byteLength, 0);
                            const audioData = new Uint8Array(totalLength);
                            let offset = 0;
                            
                            for (const chunk of audioChunks) {
                                audioData.set(new Uint8Array(chunk), offset);
                                offset += chunk.byteLength;
                            }
                            
                            ws.close();
                            resolve(audioData);
                        }
                    } catch (error) {
                        ws.close();
                        reject(error);
                    }
                };
                
                ws.onerror = (error) => {
                    console.error('WebSocket错误:', error);
                    reject(new Error('WebSocket连接失败'));
                };
                
                ws.onclose = (event) => {
                    if (event.code !== 1000) {
                        reject(new Error(`WebSocket连接异常关闭: ${event.code}`));
                    }
                };
            });
        }

        // DOM元素
        const form = document.getElementById('voiceForm');
        const voiceIdInput = document.getElementById('voiceId');
        const textContentInput = document.getElementById('textContent');
        const charCountSpan = document.getElementById('charCount');
        const generateBtn = document.getElementById('generateBtn');
        const loading = document.getElementById('loading');
        const errorMessage = document.getElementById('errorMessage');
        const audioSection = document.getElementById('audioSection');
        const audioPlayer = document.getElementById('audioPlayer');
        const downloadBtn = document.getElementById('downloadBtn');

        let currentAudioBlob = null;

        // 字符计数
        textContentInput.addEventListener('input', () => {
            const length = textContentInput.value.length;
            charCountSpan.textContent = length;

            if (length > 100) {
                charCountSpan.style.color = '#dc3545';
            } else {
                charCountSpan.style.color = '#666';
            }
        });

        // 显示错误消息
        function showError(message) {
            errorMessage.textContent = message;
            errorMessage.style.display = 'block';
        }

        // 隐藏错误消息
        function hideError() {
            errorMessage.style.display = 'none';
        }

        // 显示音频播放器
        function showAudioPlayer(audioBlob) {
            currentAudioBlob = audioBlob;
            const audioUrl = URL.createObjectURL(audioBlob);
            audioPlayer.src = audioUrl;
            audioSection.style.display = 'block';
        }

        // 下载音频
        downloadBtn.addEventListener('click', () => {
            if (currentAudioBlob) {
                const url = URL.createObjectURL(currentAudioBlob);
                const a = document.createElement('a');
                a.href = url;
                a.download = `voice_${Date.now()}.mp3`;
                document.body.appendChild(a);
                a.click();
                document.body.removeChild(a);
                URL.revokeObjectURL(url);
            }
        });

        // 表单提交
        form.addEventListener('submit', async (e) => {
            e.preventDefault();

            const voiceId = voiceIdInput.value.trim();
            const textContent = textContentInput.value.trim();

            // 验证输入
            if (!voiceId) {
                showError('请输入声音ID');
                return;
            }

            if (!textContent) {
                showError('请输入生成文本');
                return;
            }

            if (textContent.length > 100) {
                showError('生成文本不能超过100个字符');
                return;
            }

            // 隐藏错误消息和音频区域
            hideError();
            audioSection.style.display = 'none';

            // 显示加载状态
            generateBtn.disabled = true;
            loading.style.display = 'block';

            try {
                // 生成语音
                const audioData = await generateVoice(voiceId, textContent);

                // 创建音频Blob
                const audioBlob = new Blob([audioData], { type: 'audio/mpeg' });

                // 显示音频播放器
                showAudioPlayer(audioBlob);

            } catch (error) {
                console.error('生成语音失败:', error);
                showError(`生成语音失败: ${error.message}`);
            } finally {
                // 隐藏加载状态
                loading.style.display = 'none';
                generateBtn.disabled = false;
            }
        });
    </script>
</body>
</html>
