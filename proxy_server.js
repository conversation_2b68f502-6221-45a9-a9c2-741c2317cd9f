const WebSocket = require('ws');
const http = require('http');
const url = require('url');

// 创建 HTTP 服务器用于提供静态文件
const server = http.createServer((req, res) => {
    // 设置 CORS 头部
    res.setHeader('Access-Control-Allow-Origin', '*');
    res.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
    res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization');
    
    if (req.method === 'OPTIONS') {
        res.writeHead(200);
        res.end();
        return;
    }
    
    // 简单的静态文件服务
    if (req.url === '/' || req.url === '/voice_clone_demo.html') {
        const fs = require('fs');
        const path = require('path');
        
        try {
            const filePath = path.join(__dirname, 'voice_clone_demo.html');
            const content = fs.readFileSync(filePath, 'utf8');
            res.writeHead(200, { 'Content-Type': 'text/html; charset=utf-8' });
            res.end(content);
        } catch (error) {
            res.writeHead(404);
            res.end('File not found');
        }
    } else {
        res.writeHead(404);
        res.end('Not found');
    }
});

// 创建 WebSocket 服务器
const wss = new WebSocket.Server({ server });

// WebSocket 代理配置
const TARGET_HOST = 'openspeech.bytedance.com';
const TARGET_URL = `wss://${TARGET_HOST}/api/v1/tts/ws_binary`;
const TOKEN = 'Zol8wcj36NJGQC141tLZgNkvdVg-o_a7';

wss.on('connection', (clientWs, req) => {
    console.log('客户端连接到代理服务器');
    
    // 连接到目标 WebSocket 服务器
    const targetWs = new WebSocket(TARGET_URL, {
        headers: { 
            Authorization: `Bearer; ${TOKEN}` 
        }
    });
    
    // 设置二进制数据类型
    targetWs.binaryType = 'arraybuffer';
    
    targetWs.on('open', () => {
        console.log('代理服务器连接到目标服务器');
    });
    
    targetWs.on('message', (data) => {
        console.log('从目标服务器收到数据，长度:', data.length);
        // 转发数据到客户端
        if (clientWs.readyState === WebSocket.OPEN) {
            clientWs.send(data);
        }
    });
    
    targetWs.on('error', (error) => {
        console.error('目标服务器连接错误:', error);
        if (clientWs.readyState === WebSocket.OPEN) {
            clientWs.close(1011, 'Target server error');
        }
    });
    
    targetWs.on('close', (code, reason) => {
        console.log('目标服务器连接关闭:', code, reason);
        if (clientWs.readyState === WebSocket.OPEN) {
            clientWs.close(code, reason);
        }
    });
    
    // 客户端消息处理
    clientWs.on('message', (data) => {
        console.log('从客户端收到数据，长度:', data.length);
        // 转发数据到目标服务器
        if (targetWs.readyState === WebSocket.OPEN) {
            targetWs.send(data);
        }
    });
    
    clientWs.on('error', (error) => {
        console.error('客户端连接错误:', error);
        if (targetWs.readyState === WebSocket.OPEN) {
            targetWs.close();
        }
    });
    
    clientWs.on('close', (code, reason) => {
        console.log('客户端连接关闭:', code, reason);
        if (targetWs.readyState === WebSocket.OPEN) {
            targetWs.close();
        }
    });
});

const PORT = 3000;
server.listen(PORT, () => {
    console.log(`代理服务器运行在 http://localhost:${PORT}`);
    console.log(`WebSocket 代理运行在 ws://localhost:${PORT}`);
});
